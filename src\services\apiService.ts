/**
 * Modern REST API service for VPS Admin Chat
 * Handles authentication, task management, and system operations
 */

import { API_BASE_URL, HEALTH_CHECK_URL, TOKEN_STORAGE_KEY } from '@/constants';

// Core interfaces
export interface CreateTaskRequest {
  initial_prompt: string;
  tenant_id?: string;
  priority?: number;
  timeout?: number;
}

export interface CreateTaskResponse {
  task_id: string;
  status: string;
  created_at: string;
  tenant_id: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  tenant_id?: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user_id: string;
  tenant_id: string;
}

export interface UserInfo {
  user_id: string;
  tenant_id: string;
  permissions: string[];
}

export interface HealthCheckResponse {
  status: string;
  timestamp: string;
  version: string;
  uptime: number;
  active_connections: number;
  active_tasks: number;
  system_info?: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: string;
}

export interface ErrorResponse {
  success: boolean;
  error: string;
  error_code?: string;
  details?: any;
  timestamp: string;
}

class ApiService {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
    this.loadToken();
  }

  /**
   * Load token from localStorage
   */
  private loadToken(): void {
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem(TOKEN_STORAGE_KEY);
    }
  }

  /**
   * Set authentication token
   */
  setToken(token: string): void {
    this.token = token;
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem(TOKEN_STORAGE_KEY, token);
      } else {
        localStorage.removeItem(TOKEN_STORAGE_KEY);
      }
    }
  }

  /**
   * Get authentication headers
   */
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make HTTP request with error handling
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          error: `HTTP ${response.status}: ${response.statusText}`,
          success: false
        }));
        
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network request failed');
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    try {
      const response = await fetch(HEALTH_CHECK_URL, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      throw new Error(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * User authentication
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return this.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  /**
   * Get current user info
   */
  async getCurrentUser(): Promise<UserInfo> {
    return this.request<UserInfo>('/auth/me');
  }

  /**
   * Create a new task
   */
  async createTask(request: CreateTaskRequest): Promise<CreateTaskResponse> {
    return this.request<CreateTaskResponse>('/tasks', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * Get task by ID
   */
  async getTask(taskId: string): Promise<any> {
    return this.request(`/tasks/${taskId}`);
  }

  /**
   * Get all tasks for current user
   */
  async getTasks(): Promise<any[]> {
    return this.request('/tasks');
  }

  /**
   * Get system statistics
   */
  async getStats(): Promise<any> {
    return this.request('/stats');
  }

  /**
   * Test backend connection
   */
  async testConnection(): Promise<{
    success: boolean;
    message: string;
    latency?: number;
  }> {
    const startTime = Date.now();
    
    try {
      await this.healthCheck();
      const latency = Date.now() - startTime;
      
      return {
        success: true,
        message: 'Connection successful',
        latency
      };
    } catch (error) {
      const latency = Date.now() - startTime;
      
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection failed',
        latency
      };
    }
  }

  /**
   * Get comprehensive backend status
   */
  async getBackendStatus(): Promise<{
    isOnline: boolean;
    health: HealthCheckResponse | null;
    connection: { success: boolean; message: string; latency?: number };
    recommendations: string[];
  }> {
    const connection = await this.testConnection();
    let health: HealthCheckResponse | null = null;
    const recommendations: string[] = [];

    if (connection.success) {
      try {
        health = await this.healthCheck();
      } catch (error) {
        recommendations.push('Health endpoint is not responding properly');
      }
    } else {
      recommendations.push('Backend server is not reachable');
      recommendations.push('Check if the server is running on http://localhost:8001');
    }

    if (connection.latency && connection.latency > 5000) {
      recommendations.push('High latency detected - check network connection');
    }

    return {
      isOnline: connection.success,
      health,
      connection,
      recommendations
    };
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
