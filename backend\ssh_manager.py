"""
Enhanced SSH manager with connection pooling, security improvements, and multi-tenancy support.
Handles SSH connections, command execution, and system context gathering.
"""

import asyncio
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import structlog

try:
    import paramiko
    from paramiko.ssh_exception import SSH<PERSON>x<PERSON>, AuthenticationException
except ImportError:
    raise ImportError("Paramiko library not found. Please install it with 'pip install paramiko'")

from models import SSHConnection, CommandExecution, CommandResult, TenantContext
from config import settings

logger = structlog.get_logger(__name__)

@dataclass
class ConnectionInfo:
    """Information about an SSH connection."""
    client: paramiko.SSHClient
    created_at: datetime
    last_used: datetime
    tenant_id: str
    user_id: str
    connection_count: int = 0

class SSHConnectionPool:
    """Manages a pool of SSH connections for better performance and resource management."""
    
    def __init__(self, max_connections: int = 10, connection_timeout: int = 300):
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.connections: Dict[str, ConnectionInfo] = {}
        self._lock = asyncio.Lock()
    
    def _get_connection_key(self, hostname: str, username: str, tenant_id: str) -> str:
        """Generate a unique key for connection identification."""
        return f"{tenant_id}:{hostname}:{username}"
    
    async def get_connection(self, ssh_config: SSHConnection, tenant_context: TenantContext) -> paramiko.SSHClient:
        """Get or create an SSH connection."""
        connection_key = self._get_connection_key(
            ssh_config.hostname, 
            ssh_config.username, 
            tenant_context.tenant_id
        )
        
        async with self._lock:
            # Check if we have an existing valid connection
            if connection_key in self.connections:
                conn_info = self.connections[connection_key]
                
                # Check if connection is still alive and not expired
                if (conn_info.client.get_transport() and 
                    conn_info.client.get_transport().is_active() and
                    (datetime.utcnow() - conn_info.last_used).total_seconds() < self.connection_timeout):
                    
                    conn_info.last_used = datetime.utcnow()
                    conn_info.connection_count += 1
                    logger.info("Reusing existing SSH connection", 
                               connection_key=connection_key,
                               usage_count=conn_info.connection_count)
                    return conn_info.client
                else:
                    # Connection is dead or expired, remove it
                    await self._close_connection(connection_key)
            
            # Create new connection
            if len(self.connections) >= self.max_connections:
                # Remove oldest connection
                oldest_key = min(self.connections.keys(), 
                               key=lambda k: self.connections[k].last_used)
                await self._close_connection(oldest_key)
            
            # Create new SSH client
            client = await self._create_connection(ssh_config)
            
            # Store connection info
            self.connections[connection_key] = ConnectionInfo(
                client=client,
                created_at=datetime.utcnow(),
                last_used=datetime.utcnow(),
                tenant_id=tenant_context.tenant_id,
                user_id=tenant_context.user_id,
                connection_count=1
            )
            
            logger.info("Created new SSH connection", 
                       connection_key=connection_key,
                       total_connections=len(self.connections))
            
            return client
    
    async def _create_connection(self, ssh_config: SSHConnection) -> paramiko.SSHClient:
        """Create a new SSH connection."""
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            # Prepare connection parameters
            connect_params = {
                'hostname': ssh_config.hostname,
                'port': ssh_config.port,
                'username': ssh_config.username,
                'timeout': ssh_config.timeout
            }
            
            # Add authentication method
            if ssh_config.private_key_path and os.path.exists(ssh_config.private_key_path):
                connect_params['key_filename'] = ssh_config.private_key_path
            elif ssh_config.password:
                connect_params['password'] = ssh_config.password
            else:
                raise ValueError("No valid authentication method provided")
            
            # Connect in a separate thread to avoid blocking
            await asyncio.to_thread(client.connect, **connect_params)
            
            return client
            
        except Exception as e:
            client.close()
            raise ConnectionError(f"SSH connection failed: {str(e)}")
    
    async def _close_connection(self, connection_key: str):
        """Close and remove a connection."""
        if connection_key in self.connections:
            conn_info = self.connections[connection_key]
            try:
                await asyncio.to_thread(conn_info.client.close)
            except Exception as e:
                logger.warning("Error closing SSH connection", 
                             connection_key=connection_key, 
                             error=str(e))
            finally:
                del self.connections[connection_key]
                logger.info("Closed SSH connection", connection_key=connection_key)
    
    async def close_all_connections(self):
        """Close all connections in the pool."""
        async with self._lock:
            for connection_key in list(self.connections.keys()):
                await self._close_connection(connection_key)
    
    async def cleanup_expired_connections(self):
        """Remove expired connections."""
        async with self._lock:
            expired_keys = []
            for key, conn_info in self.connections.items():
                if (datetime.utcnow() - conn_info.last_used).total_seconds() > self.connection_timeout:
                    expired_keys.append(key)
            
            for key in expired_keys:
                await self._close_connection(key)
                logger.info("Cleaned up expired SSH connection", connection_key=key)

class SSHManager:
    """Enhanced SSH manager with connection pooling and security features."""
    
    def __init__(self):
        self.connection_pool = SSHConnectionPool()
        self.default_config = SSHConnection(
            hostname=settings.vps_hostname,
            port=settings.vps_port,
            username=settings.vps_username,
            password=settings.vps_password,
            private_key_path=settings.ssh_private_key_path,
            timeout=settings.ssh_timeout
        )

        # Background cleanup task will be started when needed
        self._cleanup_task = None

    def start_background_cleanup(self):
        """Start the background cleanup task if not already running."""
        if self._cleanup_task is None or self._cleanup_task.done():
            try:
                self._cleanup_task = asyncio.create_task(self._background_cleanup())
            except RuntimeError:
                # No event loop running, task will be started later
                pass
    
    async def run_command(self, 
                         command: str, 
                         tenant_context: TenantContext,
                         ssh_config: Optional[SSHConnection] = None,
                         working_directory: Optional[str] = None,
                         timeout: Optional[int] = None) -> CommandResult:
        """Execute a command on the remote server."""
        
        # Use default config if none provided
        if ssh_config is None:
            ssh_config = self.default_config
        
        # Security: Validate command for dangerous operations
        if not self._is_command_safe(command, tenant_context):
            raise ValueError(f"Command not allowed: {command}")
        
        # Get SSH connection
        client = await self.connection_pool.get_connection(ssh_config, tenant_context)
        
        # Prepare command with working directory if specified
        if working_directory:
            command = f"cd {working_directory} && {command}"
        
        # Execute command
        start_time = time.time()
        try:
            stdout, stderr, exit_code = await self._execute_command(
                client, command, timeout or ssh_config.timeout
            )
            execution_time = time.time() - start_time
            
            result = CommandResult(
                command=command,
                exit_code=exit_code,
                stdout=stdout,
                stderr=stderr,
                execution_time=execution_time
            )
            
            logger.info("Command executed", 
                       command=command[:100],  # Truncate for logging
                       exit_code=exit_code,
                       execution_time=execution_time,
                       tenant_id=tenant_context.tenant_id)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("Command execution failed", 
                        command=command[:100],
                        error=str(e),
                        execution_time=execution_time,
                        tenant_id=tenant_context.tenant_id)
            
            return CommandResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=f"Execution error: {str(e)}",
                execution_time=execution_time
            )
    
    async def _execute_command(self, client: paramiko.SSHClient, command: str, timeout: int) -> Tuple[str, str, int]:
        """Execute command using the SSH client."""
        def _sync_execute():
            _, stdout, stderr = client.exec_command(command, timeout=timeout)
            
            # Read output
            stdout_data = stdout.read().decode('utf-8', errors='ignore')
            stderr_data = stderr.read().decode('utf-8', errors='ignore')
            
            # Get exit status
            exit_code = stdout.channel.recv_exit_status()
            
            return stdout_data, stderr_data, exit_code
        
        return await asyncio.to_thread(_sync_execute)
    
    def _is_command_safe(self, command: str, tenant_context: TenantContext) -> bool:
        """Validate command safety based on tenant permissions."""
        
        # Admin users can run any command
        if "admin" in tenant_context.permissions:
            return True
        
        # Define dangerous commands
        dangerous_commands = [
            'rm -rf /', 'dd if=', 'mkfs', 'fdisk', 'parted',
            'shutdown', 'reboot', 'halt', 'poweroff',
            'passwd', 'userdel', 'groupdel',
            'iptables -F', 'ufw --force reset'
        ]
        
        # Check for dangerous patterns
        command_lower = command.lower()
        for dangerous in dangerous_commands:
            if dangerous in command_lower:
                logger.warning("Dangerous command blocked", 
                             command=command,
                             tenant_id=tenant_context.tenant_id,
                             user_id=tenant_context.user_id)
                return False
        
        return True
    
    async def gather_system_context(self, tenant_context: TenantContext) -> Tuple[str, str]:
        """Gather system context information."""
        try:
            # Helper function to run commands
            async def run_info_command(cmd: str) -> str:
                result = await self.run_command(cmd, tenant_context)
                if result.exit_code == 0:
                    return result.stdout.strip()
                return f"Command '{cmd}' failed: {result.stderr}"
            
            # Gather system information
            os_name = await run_info_command("uname -s")
            if "linux" not in os_name.lower():
                return f"Unsupported remote OS detected: {os_name}.", os_name
            
            target_os = "linux"
            os_release = await run_info_command("cat /etc/os-release")
            kernel_version = await run_info_command("uname -r")
            architecture = await run_info_command("uname -m")
            uptime = await run_info_command("uptime")
            disk_usage = await run_info_command("df -h")
            memory_info = await run_info_command("free -h")
            
            context = f"""--- REMOTE SYSTEM CONTEXT ---
Detected OS: {target_os}
Kernel Version: {kernel_version}
Architecture: {architecture}
Uptime: {uptime}

OS Details:
{os_release}

Disk Usage:
{disk_usage}

Memory Info:
{memory_info}
-----------------------------"""
            
            logger.info("System context gathered", 
                       tenant_id=tenant_context.tenant_id,
                       os=target_os)
            
            return context, target_os
            
        except Exception as e:
            error_context = f"""--- REMOTE SYSTEM CONTEXT ---
Could not be determined due to an error: {str(e)}
-----------------------------"""
            
            logger.error("Failed to gather system context", 
                        error=str(e),
                        tenant_id=tenant_context.tenant_id)
            
            return error_context, "linux"
    
    async def test_connection(self, tenant_context: TenantContext, ssh_config: Optional[SSHConnection] = None) -> bool:
        """Test SSH connection."""
        try:
            result = await self.run_command("echo 'connection test'", tenant_context, ssh_config)
            return result.exit_code == 0
        except Exception:
            return False
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        return {
            "total_connections": len(self.connection_pool.connections),
            "max_connections": self.connection_pool.max_connections,
            "connection_timeout": self.connection_pool.connection_timeout,
            "connections": [
                {
                    "key": key,
                    "tenant_id": info.tenant_id,
                    "created_at": info.created_at.isoformat(),
                    "last_used": info.last_used.isoformat(),
                    "usage_count": info.connection_count
                }
                for key, info in self.connection_pool.connections.items()
            ]
        }
    
    async def close_all_connections(self):
        """Close all SSH connections."""
        await self.connection_pool.close_all_connections()
        if hasattr(self, '_cleanup_task') and self._cleanup_task is not None:
            self._cleanup_task.cancel()
    
    async def _background_cleanup(self):
        """Background task to clean up expired connections."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self.connection_pool.cleanup_expired_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in SSH connection cleanup", error=str(e))

# Global SSH manager instance
ssh_manager = SSHManager()
