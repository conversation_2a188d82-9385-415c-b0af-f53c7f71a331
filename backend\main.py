"""
Main FastAPI application for the WebSocket-based VPS Admin backend.
Includes WebSocket endpoints, REST API routes, middleware, and lifecycle management.
"""

import asyncio
import json
import warnings
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any

# Suppress cryptography deprecation warnings from paramiko
warnings.filterwarnings("ignore", category=DeprecationWarning, module="paramiko")
warnings.filterwarnings("ignore", message=".*TripleDES.*", category=DeprecationWarning)

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog

from config import settings, get_cors_origins
from models import WebSocketMessage, MessageType, TenantContext
from auth import get_current_user, auth_manager
from api_routes import router as api_router
from websocket_manager import websocket_manager
from task_manager import task_manager
from ssh_manager import ssh_manager
from ai_client import get_ai_client

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Application startup and shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting VPS Admin WebSocket Backend", version=settings.app_version)
    
    try:
        # Initialize AI client
        ai_client = get_ai_client()
        logger.info("AI client initialized")

        # Start SSH manager background cleanup
        ssh_manager.start_background_cleanup()
        logger.info("SSH manager background cleanup started")

        # Test SSH connection if configured
        if settings.debug:
            try:
                # Create a temporary tenant context for testing
                test_context = TenantContext(
                    tenant_id=settings.default_tenant_id,
                    user_id="system",
                    permissions=["admin"]
                )
                
                connection_test = await ssh_manager.test_connection(test_context)
                if connection_test:
                    logger.info("SSH connection test successful")
                    
                    # Gather system context
                    context, os_type = await ssh_manager.gather_system_context(test_context)
                    logger.info("System context gathered", os_type=os_type)
                else:
                    logger.warning("SSH connection test failed")
            except Exception as e:
                logger.warning("SSH initialization failed", error=str(e))
        
        logger.info("Backend startup completed successfully")
        
    except Exception as e:
        logger.error("Backend startup failed", error=str(e))
        raise
    
    yield  # Application runs here
    
    # Shutdown
    logger.info("Shutting down VPS Admin WebSocket Backend")
    
    try:
        # Close all WebSocket connections
        await websocket_manager.close_all_connections()
        
        # Close all SSH connections
        await ssh_manager.close_all_connections()
        
        logger.info("Backend shutdown completed")
        
    except Exception as e:
        logger.error("Error during shutdown", error=str(e))

# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="A modern WebSocket-based VPS administration backend with concurrent task handling and multi-tenancy support",
    version=settings.app_version,
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", settings.vps_hostname]
    )

# Custom middleware for request logging
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all HTTP requests."""
    start_time = datetime.utcnow()
    
    response = await call_next(request)
    
    process_time = (datetime.utcnow() - start_time).total_seconds()
    
    logger.info("HTTP request",
                method=request.method,
                url=str(request.url),
                status_code=response.status_code,
                process_time=process_time)
    
    return response

# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    logger.warning("HTTP exception",
                   status_code=exc.status_code,
                   detail=exc.detail,
                   url=str(request.url))
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error("Unhandled exception",
                 error=str(exc),
                 url=str(request.url),
                 exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# WebSocket endpoint for real-time communication
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """WebSocket endpoint for real-time task communication."""

    logger.info("WebSocket connection attempt", token_provided=bool(token), client_host=websocket.client.host if websocket.client else "unknown")

    # Authenticate user
    if not token:
        logger.warning("WebSocket connection rejected: no token provided")
        await websocket.close(code=1008, reason="Authentication token required")
        return

    try:
        # Verify token
        logger.info("Verifying WebSocket token", token=token[:10] + "..." if len(token) > 10 else token)
        token_data = auth_manager.verify_token(token)
        if not token_data:
            logger.warning("WebSocket connection rejected: invalid token", token=token)
            await websocket.close(code=1008, reason="Invalid authentication token")
            return
        
        user_context = auth_manager.get_current_user_context(token_data)
        
        # Connect to WebSocket manager
        connection_id = await websocket_manager.connect(
            websocket, 
            user_context.user_id, 
            user_context.tenant_id
        )
        
        logger.info("WebSocket connection established",
                   connection_id=connection_id,
                   user_id=user_context.user_id,
                   tenant_id=user_context.tenant_id)
        
        try:
            # Handle incoming messages
            while True:
                try:
                    # Receive message from client
                    data = await websocket.receive_text()
                    
                    # Parse message
                    try:
                        message_data = json.loads(data)
                        action = message_data.get("action")
                        
                        if action == "send_message":
                            # Handle task message
                            task_id = message_data.get("task_id")
                            message = message_data.get("message", "")
                            
                            if not task_id:
                                error_msg = WebSocketMessage(
                                    type=MessageType.ERROR,
                                    content="Task ID is required",
                                    user_id=user_context.user_id,
                                    tenant_id=user_context.tenant_id
                                )
                                await websocket_manager.send_to_connection(connection_id, error_msg)
                                continue
                            
                            # Subscribe to task updates
                            await websocket_manager.subscribe_to_task(connection_id, task_id)
                            
                            # Process task message and stream responses
                            async for ws_message in task_manager.process_task_message(
                                task_id, message, user_context
                            ):
                                await websocket_manager.send_to_connection(connection_id, ws_message)
                        else:
                            # Handle other WebSocket commands
                            response = await websocket_manager.handle_message(connection_id, data)

                            # Only send response for non-heartbeat commands to avoid spam
                            # Heartbeat responses are handled internally and don't need to be sent to frontend
                            try:
                                command_data = json.loads(data)
                                if command_data.get("action") != "heartbeat":
                                    # Send response for non-heartbeat commands
                                    response_msg = WebSocketMessage(
                                        type=MessageType.INFO,
                                        content=response.message,  # Send just the message, not the full object
                                        user_id=user_context.user_id,
                                        tenant_id=user_context.tenant_id
                                    )
                                    await websocket_manager.send_to_connection(connection_id, response_msg)
                            except json.JSONDecodeError:
                                # If we can't parse the command, send the response anyway
                                response_msg = WebSocketMessage(
                                    type=MessageType.INFO,
                                    content=response.message,
                                    user_id=user_context.user_id,
                                    tenant_id=user_context.tenant_id
                                )
                                await websocket_manager.send_to_connection(connection_id, response_msg)
                    
                    except json.JSONDecodeError:
                        error_msg = WebSocketMessage(
                            type=MessageType.ERROR,
                            content="Invalid JSON message format",
                            user_id=user_context.user_id,
                            tenant_id=user_context.tenant_id
                        )
                        await websocket_manager.send_to_connection(connection_id, error_msg)
                
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error("Error processing WebSocket message",
                               connection_id=connection_id,
                               error=str(e))
                    
                    error_msg = WebSocketMessage(
                        type=MessageType.ERROR,
                        content=f"Message processing error: {str(e)}",
                        user_id=user_context.user_id,
                        tenant_id=user_context.tenant_id
                    )
                    await websocket_manager.send_to_connection(connection_id, error_msg)
        
        finally:
            # Disconnect from WebSocket manager
            await websocket_manager.disconnect(connection_id)
            logger.info("WebSocket connection closed",
                       connection_id=connection_id,
                       user_id=user_context.user_id)
    
    except Exception as e:
        logger.error("WebSocket connection error", error=str(e))
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass

# Health check endpoint (outside API prefix for load balancers)
@app.get("/health")
async def health_check():
    """Simple health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.app_version
    }

# Root endpoint
@app.get("/")
async def read_root():
    """Root endpoint."""
    return {
        "service": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "timestamp": datetime.utcnow().isoformat(),
        "docs_url": "/docs" if settings.debug else None
    }

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
