"""
Enhanced task manager with concurrent execution, proper isolation, and multi-tenancy support.
Handles task lifecycle, state management, and WebSocket communication.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
import structlog

from models import (
    Task, TaskStatus, TaskPlan, CreateTaskRequest, CreateTaskResponse,
    WebSocketMessage, MessageType, TenantContext
)
from websocket_manager import websocket_manager
from ssh_manager import ssh_manager
from config import settings

logger = structlog.get_logger(__name__)

class TaskState:
    """Internal task state management."""
    
    def __init__(self, task: Task):
        self.task = task
        self.lock = asyncio.Lock()
        self.ai_processing = False
        self.awaiting_form_response = False
        self.awaiting_confirmation = False
        self.awaiting_interactive_confirmation = False
        self.current_form_id: Optional[str] = None
        self.command_to_run: Optional[str] = None
        self.ssh_manager = None
        self.ai_client = None
        self.created_at = datetime.utcnow()
        self.last_activity = datetime.utcnow()

    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity = datetime.utcnow()

    def is_expired(self) -> bool:
        """Check if task has expired."""
        return (datetime.utcnow() - self.created_at).total_seconds() > self.task.timeout

class TaskManager:
    """Manages task lifecycle and execution with concurrency support."""
    
    def __init__(self):
        # Task storage: task_id -> TaskState
        self.tasks: Dict[str, TaskState] = {}
        
        # User tasks mapping: user_id -> List[task_id]
        self.user_tasks: Dict[str, List[str]] = {}
        
        # Tenant tasks mapping: tenant_id -> List[task_id]
        self.tenant_tasks: Dict[str, List[str]] = {}
        
        # Global lock for task management operations
        self._global_lock = asyncio.Lock()
        
        # Background cleanup task
        self._cleanup_task = None
        
        # Note: Removed global execution semaphore to allow concurrent task processing
        # Individual task locks provide sufficient concurrency control

    async def create_task(self, request: CreateTaskRequest, user_context: TenantContext) -> CreateTaskResponse:
        """Create a new task."""
        task_id = str(uuid.uuid4())
        # Create task model
        task = Task(
            id=task_id,
            user_id=user_context.user_id,
            tenant_id=user_context.tenant_id,
            initial_prompt=request.initial_prompt,
            status=TaskStatus.PENDING,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            priority=request.priority,
            timeout=request.timeout or settings.task_timeout
        )
        
        # Create task state
        task_state = TaskState(task)
        async with self._global_lock:
            # Check user task limits (optional)
            user_task_count = len(self.user_tasks.get(user_context.user_id, []))
            if user_task_count >= 10:  # Configurable limit
                raise Exception("Too many active tasks for user")

            # Store task
            self.tasks[task_id] = task_state
            
            # Update mappings
            if user_context.user_id not in self.user_tasks:
                self.user_tasks[user_context.user_id] = []
            self.user_tasks[user_context.user_id].append(task_id)
            
            if user_context.tenant_id not in self.tenant_tasks:
                self.tenant_tasks[user_context.tenant_id] = []
            self.tenant_tasks[user_context.tenant_id].append(task_id)
        
        logger.info("Task created", 
                   task_id=task_id, 
                   user_id=user_context.user_id, 
                   tenant_id=user_context.tenant_id)
        
        # Start cleanup task if not running
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_tasks())
        
        return CreateTaskResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            created_at=task.created_at,
            tenant_id=task.tenant_id
        )

    async def get_task(self, task_id: str, user_context: TenantContext) -> Optional[Task]:
        """Get a task by ID with access control."""
        if task_id not in self.tasks:
            return None
        
        task_state = self.tasks[task_id]
        task = task_state.task
        
        # Check access permissions
        if task.user_id != user_context.user_id and task.tenant_id != user_context.tenant_id:
            if "admin" not in user_context.permissions:
                return None
        
        return task

    async def update_task_status(self, task_id: str, status: TaskStatus, error_message: Optional[str] = None):
        """Update task status."""
        if task_id not in self.tasks:
            return False
        
        task_state = self.tasks[task_id]
        async with task_state.lock:
            task_state.task.status = status
            task_state.task.updated_at = datetime.utcnow()
            task_state.update_activity()
            
            if error_message:
                task_state.task.error_message = error_message
            
            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                task_state.task.completed_at = datetime.utcnow()

        # Note: Removed _notify_task_update to prevent JSON messages in UI
        # Task completion is already handled by TASK_END messages

        return True

    async def delete_task(self, task_id: str, user_context: TenantContext) -> bool:
        """Delete a task with access control."""
        if task_id not in self.tasks:
            return False
        
        task_state = self.tasks[task_id]
        task = task_state.task
        
        # Check access permissions
        if task.user_id != user_context.user_id:
            if "admin" not in user_context.permissions:
                return False
        
        async with self._global_lock:
            # Remove from storage
            del self.tasks[task_id]
            
            # Update mappings
            if task.user_id in self.user_tasks:
                if task_id in self.user_tasks[task.user_id]:
                    self.user_tasks[task.user_id].remove(task_id)
                if not self.user_tasks[task.user_id]:
                    del self.user_tasks[task.user_id]
            
            if task.tenant_id in self.tenant_tasks:
                if task_id in self.tenant_tasks[task.tenant_id]:
                    self.tenant_tasks[task.tenant_id].remove(task_id)
                if not self.tenant_tasks[task.tenant_id]:
                    del self.tenant_tasks[task.tenant_id]
        
        logger.info("Task deleted", task_id=task_id, user_id=user_context.user_id)
        return True

    async def process_task_message(self, task_id: str, message: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Process a message for a task and yield WebSocket messages."""
        if task_id not in self.tasks:
            yield WebSocketMessage(
                type=MessageType.ERROR,
                content="Task not found",
                task_id=task_id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            return

        task_state = self.tasks[task_id]
        # Check access permissions
        if task_state.task.user_id != user_context.user_id:
            if "admin" not in user_context.permissions:
                yield WebSocketMessage(
                    type=MessageType.ERROR,
                    content="Access denied",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                return

        # Update task state
        async with task_state.lock:
            task_state.update_activity()

            # Update task status to running if pending
            if task_state.task.status == TaskStatus.PENDING:
                task_state.task.status = TaskStatus.RUNNING
                task_state.task.updated_at = datetime.utcnow()

        # Process the message based on current task state
        async for ws_message in self._handle_task_logic(task_state, message, user_context):
            yield ws_message

    async def _handle_task_logic(self, task_state: TaskState, message: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Handle task logic and state transitions."""
        task_id = task_state.task.id
        try:
            # Initialize AI client and SSH manager if needed
            if not task_state.ai_client:
                from ai_client import get_ai_client
                task_state.ai_client = get_ai_client()

            if not task_state.ssh_manager:
                task_state.ssh_manager = ssh_manager

            # CHECK FOR AI PROCESSING - PREVENT DUPLICATE CALLS
            if task_state.ai_processing:
                yield WebSocketMessage(
                    type=MessageType.INFO,
                    content="AI is currently processing your request. Please wait...",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                return
            # Route to appropriate handler based on task state
            if task_state.awaiting_form_response:
                async for msg in self._handle_form_response(task_state, message, user_context):
                    yield msg

            elif task_state.awaiting_confirmation:
                async for msg in self._handle_confirmation(task_state, message, user_context):
                    yield msg

            elif task_state.awaiting_interactive_confirmation:
                async for msg in self._handle_interactive_confirmation(task_state, message, user_context):
                    yield msg

            elif not task_state.task.task_plan:
                # Check if this is a blank task (placeholder prompt)
                if task_state.task.initial_prompt == "New Task":
                    # This is a blank task - ask user what they want to do
                    yield WebSocketMessage(
                        type=MessageType.AI_RESPONSE,
                        content="Hello! I'm ready to help you with your server administration tasks. What would you like me to do?",
                        task_id=task_id,
                        user_id=user_context.user_id,
                        tenant_id=user_context.tenant_id
                    )
                    # Update the task with the user's actual request
                    if message.strip() and message.strip() != "What would you like me to help you with?":
                        task_state.task.initial_prompt = message.strip()
                        task_state.task.updated_at = datetime.utcnow()
                        # Now generate the task plan with the actual request
                        if not task_state.ai_processing:
                            async for msg in self._generate_task_plan(task_state, message, user_context):
                                yield msg
                        else:
                            yield WebSocketMessage(
                                type=MessageType.INFO,
                                content="AI is already generating your task plan. Please wait...",
                                task_id=task_id,
                                user_id=user_context.user_id,
                                tenant_id=user_context.tenant_id
                            )
                else:
                    # Generate initial task plan for regular tasks
                    if not task_state.ai_processing:
                        async for msg in self._generate_task_plan(task_state, message, user_context):
                            yield msg
                    else:
                        yield WebSocketMessage(
                            type=MessageType.INFO,
                            content="AI is already generating your task plan. Please wait...",
                            task_id=task_id,
                            user_id=user_context.user_id,
                            tenant_id=user_context.tenant_id
                        )

            else:
                yield WebSocketMessage(
                    type=MessageType.AI_RESPONSE,
                    content="I'm currently waiting for you to respond to the last prompt (e.g., approve a command or fill a form).",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )

            # Process current step if not waiting for anything and task is still active
            if (not any([task_state.awaiting_form_response, task_state.awaiting_confirmation,
                        task_state.awaiting_interactive_confirmation, task_state.ai_processing])
                and task_state.task.task_plan
                and task_state.task.status not in [TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.COMPLETED]):
                async for msg in self._process_current_step(task_state, user_context):
                    yield msg

        except Exception as e:
            logger.error("Error in task logic", task_id=task_id, error=str(e))
            yield WebSocketMessage(
                type=MessageType.ERROR,
                content=f"Task processing error: {str(e)}",
                task_id=task_id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            await self.update_task_status(task_id, TaskStatus.FAILED, str(e))

    async def _generate_task_plan(self, task_state: TaskState, message: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Generate AI task plan."""
        task_id = task_state.task.id
        task_state.ai_processing = True

        try:
            # Get system context
            context, target_os = await task_state.ssh_manager.gather_system_context(user_context)

            # Generate task plan using AI
            response = await task_state.ai_client.generate_task_plan(message, target_os, context)
            if response is None or response.get("is_error"):
                error_msg = response['data']['message'] if response else "An unknown error occurred with the AI."
                yield WebSocketMessage(
                    type=MessageType.ERROR,
                    content=error_msg,
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                yield WebSocketMessage(
                    type=MessageType.TASK_END,
                    content="Task failed to generate.",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                await self.update_task_status(task_id, TaskStatus.FAILED, error_msg)
                # SSH connections are managed by connection pool
                return

            # Store task plan
            task_plan_data = response["data"]
            task_state.task.task_plan = TaskPlan(**task_plan_data)
            task_state.task.current_step_index = 0
            task_state.task.updated_at = datetime.utcnow()
        except Exception as e:
            logger.error("Error generating task plan", task_id=task_id, error=str(e))
            yield WebSocketMessage(
                type=MessageType.ERROR,
                content=f"Unexpected error during AI processing: {e}",
                task_id=task_id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            await self.update_task_status(task_id, TaskStatus.FAILED, str(e))
            # SSH connections are managed by connection pool

        finally:
            task_state.ai_processing = False

    async def _process_current_step(self, task_state: TaskState, user_context: TenantContext, send_completion_messages: bool = True) -> AsyncGenerator[WebSocketMessage, None]:
        """Process the current step in the task plan."""
        task_id = task_state.task.id

        # Check if we should skip processing
        if any([task_state.awaiting_confirmation, task_state.awaiting_form_response,
               task_state.awaiting_interactive_confirmation, task_state.ai_processing]):
            return

        if not task_state.task.task_plan:
            return

        steps = task_state.task.task_plan.steps
        current_index = task_state.task.current_step_index

        if current_index >= len(steps):
            # Task completed - check if completion messages have already been sent
            if send_completion_messages and not getattr(task_state, 'completion_messages_sent', False):
                # Send completion messages only once
                task_state.completion_messages_sent = True

                conclusion_text = getattr(task_state.task.task_plan, 'conclusion', 'Task finished.')
                final_conclusion = self._substitute_placeholders(conclusion_text, task_state.task.placeholder_values, is_description=True)

                yield WebSocketMessage(
                    type=MessageType.AI_RESPONSE,
                    content=f"**{final_conclusion}**",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                yield WebSocketMessage(
                    type=MessageType.TASK_END,
                    content="Task completed successfully.",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )

            await self.update_task_status(task_id, TaskStatus.COMPLETED)
            # SSH connections are managed by connection pool
            return

        step = steps[current_index]
        command = step.command

        # Check for placeholders in command
        placeholders = self._extract_placeholders(command)
        missing_placeholders = placeholders - set(task_state.task.placeholder_values.keys())

        if missing_placeholders:
            # Request form input for missing placeholders
            async for msg in self._request_form_input(task_state, step, missing_placeholders, user_context):
                yield msg
            return

        # Substitute placeholders
        final_command = self._substitute_placeholders(command, task_state.task.placeholder_values)
        task_state.command_to_run = final_command

        # Show step info with placeholder substitution
        step_description = self._substitute_placeholders(step.description, task_state.task.placeholder_values, is_description=True)
        total_steps = len(steps)
        info_message = f"**Step {current_index + 1}/{total_steps}**: {step_description}" if total_steps > 1 else f"**{step_description}**"

        yield WebSocketMessage(
            type=MessageType.INFO,
            content=info_message,
            task_id=task_id,
            user_id=user_context.user_id,
            tenant_id=user_context.tenant_id
        )

        if step.interactive:
            # Request confirmation for interactive commands
            task_state.awaiting_interactive_confirmation = True

            yield WebSocketMessage(
                type=MessageType.INTERACTIVE_COMMAND_CONFIRMATION,
                content=final_command,
                task_id=task_id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
        else:
            # Request confirmation for regular commands
            task_state.awaiting_confirmation = True

            yield WebSocketMessage(
                type=MessageType.COMMAND_CONFIRMATION,
                content=final_command,
                metadata={"danger_level": step.danger_level.value},
                task_id=task_id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )

    async def _handle_form_response(self, task_state: TaskState, message: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Handle form response."""
        try:
            import json
            form_data = json.loads(message)
            task_state.task.placeholder_values.update(form_data)
            task_state.awaiting_form_response = False
            # Continue processing the current step after form submission
            # Note: Form responses should not trigger task completion directly
            async for msg in self._process_current_step(task_state, user_context, send_completion_messages=False):
                yield msg
        except json.JSONDecodeError:
            yield WebSocketMessage(
                type=MessageType.ERROR,
                content="Invalid form data received. Please try submitting again.",
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )

    async def _handle_confirmation(self, task_state: TaskState, message: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Handle command confirmation."""
        prompt = message.lower().strip()
        task_state.awaiting_confirmation = False

        if "yes" in prompt:
            command_to_run = task_state.command_to_run or ""
            was_successful = False
            async for msg in self._execute_command_with_retry(task_state, command_to_run, user_context):
                # Check if the event signals success
                if msg.type == MessageType.STEP_SUCCESS:
                    was_successful = True
                yield msg

            if was_successful:
                task_state.task.current_step_index += 1
                # Check if task is complete before processing next step
                if task_state.task.task_plan and task_state.task.current_step_index >= len(task_state.task.task_plan.steps):
                    # Task completed - send completion messages only if not already sent
                    if not getattr(task_state, 'completion_messages_sent', False):
                        task_state.completion_messages_sent = True

                        conclusion_text = getattr(task_state.task.task_plan, 'conclusion', 'Task finished.')
                        final_conclusion = self._substitute_placeholders(conclusion_text, task_state.task.placeholder_values, is_description=True)

                        yield WebSocketMessage(
                            type=MessageType.AI_RESPONSE,
                            content=f"**{final_conclusion}**",
                            task_id=task_state.task.id,
                            user_id=user_context.user_id,
                            tenant_id=user_context.tenant_id
                        )
                        yield WebSocketMessage(
                            type=MessageType.TASK_END,
                            content="Task completed successfully.",
                            task_id=task_state.task.id,
                            user_id=user_context.user_id,
                            tenant_id=user_context.tenant_id
                        )

                    await self.update_task_status(task_state.task.id, TaskStatus.COMPLETED)
                else:
                    # Continue processing the next step after successful execution
                    # Don't send completion messages here as they're handled above
                    async for msg in self._process_current_step(task_state, user_context, send_completion_messages=False):
                        yield msg
            else:
                await self.update_task_status(task_state.task.id, TaskStatus.FAILED, "Command execution failed")
                # SSH connections are managed by connection pool
        else:
            # Handle "no" response similar to error case
            error_msg = "User declined command execution."
            yield WebSocketMessage(
                type=MessageType.ERROR,
                content=error_msg,
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            yield WebSocketMessage(
                type=MessageType.TASK_END,
                content="Task aborted by user.",
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            await self.update_task_status(task_state.task.id, TaskStatus.FAILED, error_msg)
            # SSH connections are managed by connection pool
            return

    async def _handle_interactive_confirmation(self, task_state: TaskState, message: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Handle interactive command confirmation."""
        prompt = message.lower().strip()
        task_state.awaiting_interactive_confirmation = False
        command_to_run = task_state.command_to_run or ""
        final_command = command_to_run

        if "auto" in prompt:
            yield WebSocketMessage(
                type=MessageType.INFO,
                content=f"Executing in AUTO mode: `{command_to_run}`",
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            final_command = f"DEBIAN_FRONTEND=noninteractive {command_to_run} -y"
        elif "manual" in prompt:
            yield WebSocketMessage(
                type=MessageType.INFO,
                content=f"Executing in MANUAL mode: `{command_to_run}`.",
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
        else:
            # Handle "no" response similar to error case
            error_msg = "User declined interactive command execution."
            yield WebSocketMessage(
                type=MessageType.ERROR,
                content=error_msg,
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            yield WebSocketMessage(
                type=MessageType.TASK_END,
                content="Task aborted by user.",
                task_id=task_state.task.id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )
            await self.update_task_status(task_state.task.id, TaskStatus.FAILED, error_msg)
            # SSH connections are managed by connection pool
            return

        was_successful = False
        async for msg in self._execute_command_with_retry(task_state, final_command, user_context):
            if msg.type == MessageType.STEP_SUCCESS:
                was_successful = True
            yield msg

        if was_successful:
            task_state.task.current_step_index += 1
            # Check if task is complete before processing next step
            if task_state.task.task_plan and task_state.task.current_step_index >= len(task_state.task.task_plan.steps):
                # Task completed - send completion messages only if not already sent
                if not getattr(task_state, 'completion_messages_sent', False):
                    task_state.completion_messages_sent = True

                    conclusion_text = getattr(task_state.task.task_plan, 'conclusion', 'Task finished.')
                    final_conclusion = self._substitute_placeholders(conclusion_text, task_state.task.placeholder_values, is_description=True)

                    yield WebSocketMessage(
                        type=MessageType.AI_RESPONSE,
                        content=f"**{final_conclusion}**",
                        task_id=task_state.task.id,
                        user_id=user_context.user_id,
                        tenant_id=user_context.tenant_id
                    )
                    yield WebSocketMessage(
                        type=MessageType.TASK_END,
                        content="Task completed successfully.",
                        task_id=task_state.task.id,
                        user_id=user_context.user_id,
                        tenant_id=user_context.tenant_id
                    )

                await self.update_task_status(task_state.task.id, TaskStatus.COMPLETED)
            else:
                # Continue processing the next step after successful execution
                # Don't send completion messages here as they're handled above
                async for msg in self._process_current_step(task_state, user_context, send_completion_messages=False):
                    yield msg
        else:
            await self.update_task_status(task_state.task.id, TaskStatus.FAILED, "Command execution failed")
            # SSH connections are managed by connection pool

    async def _execute_command_with_retry(self, task_state: TaskState, command: str, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Execute a command with retry logic and AI-generated corrections."""
        MAX_RETRIES = 3
        retries = 0
        current_command = command
        failed_history = []

        task_id = task_state.task.id
        step_index = task_state.task.current_step_index
        step_description = "current step"

        if task_state.task.task_plan and step_index < len(task_state.task.task_plan.steps):
            step_description = self._substitute_placeholders(
                task_state.task.task_plan.steps[step_index].description,
                task_state.task.placeholder_values,
                is_description=False  # For AI prompts, don't add quotes or 'placeholders' word
            )

        while retries < MAX_RETRIES:
            if retries > 0:
                yield WebSocketMessage(
                    type=MessageType.INFO,
                    content=f"Retrying... (Attempt {retries + 1}/{MAX_RETRIES})",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )

            # Execute command via SSH
            result = await task_state.ssh_manager.run_command(current_command, user_context)

            # Send SSH output
            yield WebSocketMessage(
                type=MessageType.SSH_OUTPUT,
                content={
                    "command": current_command,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "exit_status": result.exit_code,
                    "success": result.exit_code == 0,
                    "execution_time": int(result.execution_time * 1000)
                },
                task_id=task_id,
                user_id=user_context.user_id,
                tenant_id=user_context.tenant_id
            )

            if result.exit_code == 0:
                yield WebSocketMessage(
                    type=MessageType.STEP_SUCCESS,
                    content=f"Command executed successfully.",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                return

            retries += 1
            failed_history.append({"command": current_command, "stdout": result.stdout, "stderr": result.stderr})

            if retries >= MAX_RETRIES:
                break

            # PROTECT AI CORRECTION CALLS
            if task_state.ai_processing:
                yield WebSocketMessage(
                    type=MessageType.INFO,
                    content="AI is already processing a correction. Please wait...",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )
                return

            task_state.ai_processing = True
            try:
                yield WebSocketMessage(
                    type=MessageType.INFO,
                    content="Command failed. Asking AI for a potential fix...",
                    task_id=task_id,
                    user_id=user_context.user_id,
                    tenant_id=user_context.tenant_id
                )

                # Generate correction command using AI
                alternative_command = await task_state.ai_client.generate_correction_command(
                    f"Step: {step_description}",
                    failed_history
                )

                if alternative_command and alternative_command.strip().upper() != "NO_ALTERNATIVE":
                    # Show AI response with the suggested command

                    # Store the AI suggested command and request standard confirmation
                    task_state.command_to_run = alternative_command
                    task_state.awaiting_confirmation = True

                    yield WebSocketMessage(
                        type=MessageType.COMMAND_CONFIRMATION,
                        content=alternative_command,
                        metadata={
                            "danger_level": "caution",
                            "ai_suggested": True,
                            "original_command": current_command,
                            "retry_attempt": retries
                        },
                        task_id=task_id,
                        user_id=user_context.user_id,
                        tenant_id=user_context.tenant_id
                    )
                    return  # Exit the retry loop and wait for user confirmation
                else:
                    yield WebSocketMessage(
                        type=MessageType.ERROR,
                        content="AI could not determine an alternative command.",
                        task_id=task_id,
                        user_id=user_context.user_id,
                        tenant_id=user_context.tenant_id
                    )
                    break
            finally:
                task_state.ai_processing = False

        yield WebSocketMessage(
            type=MessageType.ERROR,
            content=f"Command failed after {retries} attempt(s). The step could not be completed.",
            task_id=task_id,
            user_id=user_context.user_id,
            tenant_id=user_context.tenant_id
        )
        yield WebSocketMessage(
            type=MessageType.AI_RESPONSE,
            content="The automated process has been halted. Please describe a different approach or start a new task.",
            task_id=task_id,
            user_id=user_context.user_id,
            tenant_id=user_context.tenant_id
        )

    async def _request_form_input(self, task_state: TaskState, step, missing_placeholders: set, user_context: TenantContext) -> AsyncGenerator[WebSocketMessage, None]:
        """Request form input for missing placeholders."""
        form_id = str(uuid.uuid4())
        form_fields = [{"name": ph, "required": True} for ph in missing_placeholders]

        # Apply placeholder substitution to the step description for display
        formatted_description = self._substitute_placeholders(step.description, task_state.task.placeholder_values, is_description=True)

        form_request = {
            "form_id": form_id,
            "title": "🤖 AI Needs More Information",
            "description": formatted_description,
            "fields": form_fields,
        }

        task_state.awaiting_form_response = True
        task_state.current_form_id = form_id

        # Send form request
        yield WebSocketMessage(
            type=MessageType.FORM_REQUEST,
            content=form_request,
            task_id=task_state.task.id,
            user_id=user_context.user_id,
            tenant_id=user_context.tenant_id
        )

    def _extract_placeholders(self, command: str) -> set:
        """Extract placeholders from command string using @@placeholder@@ format."""
        import re
        if not command:
            return set()
        return set(re.findall(r"@@([a-zA-Z0-9_]+)@@", command))

    def _substitute_placeholders(self, command: str, values: Dict[str, str], is_description: bool = False) -> str:
        """Substitute placeholders in command with values using @@placeholder@@ format."""
        import re
        if not command:
            return ""

        # First, substitute placeholders that have values
        for key, value in values.items():
            command = command.replace(f"@@{key}@@", value)

        # For remaining placeholders without values, format them for display:
        # Remove '@@', replace '_' with ' ', and add quotes for descriptions
        def format_placeholder(match):
            placeholder = match.group(1)  # Extract the placeholder name
            # Replace underscores with spaces
            formatted = placeholder.replace('_', ' ')
            # Add single quotes around placeholders in descriptions and add 'placeholders' word
            if is_description:
                formatted = f"'{formatted}'"
            return formatted

        # Apply formatting to remaining placeholders
        command = re.sub(r"@@([a-zA-Z0-9_]+)@@", format_placeholder, command)
        return command



    async def _cleanup_expired_tasks(self):
        """Background task to clean up expired tasks."""
        while True:
            try:
                await asyncio.sleep(settings.cleanup_interval)
                
                expired_tasks = []
                for task_id, task_state in self.tasks.items():
                    if task_state.is_expired():
                        expired_tasks.append(task_id)
                
                for task_id in expired_tasks:
                    await self.update_task_status(task_id, TaskStatus.FAILED, "Task expired")
                    logger.info("Cleaned up expired task", task_id=task_id)
                
            except Exception as e:
                logger.error("Error in task cleanup", error=str(e))

    async def get_user_tasks(self, user_context: TenantContext) -> List[Task]:
        """Get all tasks for a user."""
        task_ids = self.user_tasks.get(user_context.user_id, [])
        tasks = []
        
        for task_id in task_ids:
            if task_id in self.tasks:
                tasks.append(self.tasks[task_id].task)
        
        return tasks

    async def get_stats(self) -> Dict[str, Any]:
        """Get task manager statistics."""
        status_counts = {}
        for task_state in self.tasks.values():
            status = task_state.task.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_tasks": len(self.tasks),
            "status_counts": status_counts,
            "unique_users": len(self.user_tasks),
            "unique_tenants": len(self.tenant_tasks)
        }

# Global task manager instance
task_manager = TaskManager()
