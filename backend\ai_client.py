# ai_client.py
import os
import json
import re
import asyncio
from typing import Dict, Optional, List

# Using the correct import structure for google-generativeai
import google.generativeai as genai
from jsonschema import validate, ValidationError

# Task generation schema
TASK_SCHEMA = {
    "type": "object",
    "properties": {
        "task_name": {"type": "string"},
        "description": {"type": "string"},
        "estimated_time": {"type": "string"},
        "steps": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "description": {"type": "string"},
                    "command": {"type": "string"},
                    "rollback_command": {"type": "string"},
                    "danger_level": {"type": "string", "enum": ["safe", "caution", "dangerous"]},
                    "interactive": {"type": "boolean"}
                },
                "required": ["description", "command", "interactive"]
            }
        },
        "conclusion": {"type": "string"}
    },
    "required": ["task_name", "description", "steps", "conclusion"]
}
ERROR_SCHEMA = {
    "type": "object",
    "properties": { "message": {"type": "string"}, },
    "required": ["message"]
}

class AIClient:
    """Handles all interactions with the Google Gemini API."""

    def __init__(self, api_key: str):
        if not api_key:
            raise ValueError("GEMINI_API_KEY is required to initialize the AIClient.")

        # Configure the API key
        genai.configure(api_key=api_key)
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-pro-latest")

    def _execute_gemini_call(self, prompt: str, config_params: Dict) -> str:
        """
        A centralized, synchronous method for calling the Gemini API.
        """
        print("Executing Gemini Call")

        # Create the model
        model = genai.GenerativeModel(self.model_name)

        # Generate response
        response = model.generate_content(
            prompt,
            generation_config=config_params
        )

        return response.text if response.text else ""

    def _sync_generate_task_plan(self, user_request: str, target_os: str, system_context: str) -> Optional[Dict]:
        """Internal sync method to generate the main task plan."""

        # Create system instruction + user prompt
        system_instruction = """You are a server system administrator expert.
Generate only valid JSON responses for task breakdowns.
Focus on practical, safe, and tested commands.
If you cannot fulfill a request, you MUST return the specified error JSON format."""

        prompt = f"{system_instruction}\n\n{self._create_task_prompt(user_request, target_os, system_context)}"

        # Define the configuration for this specific call
        task_plan_config = {
            "response_mime_type": "application/json",
            "temperature": 1,
        }

        try:
            response_text = self._execute_gemini_call(prompt, task_plan_config)
            return self._validate_and_parse_response(response_text)
        except Exception as e:
            print(f"Error calling Gemini API for task plan: {e}")
            return {"is_error": True, "data": {"message": f"Connection Error: {e}"}}

    async def generate_task_plan(self, user_request: str, target_os: str, system_context: str) -> Optional[Dict]:
        """Asynchronously generates a task plan by running the sync method in a thread."""
        return await asyncio.to_thread(
            self._sync_generate_task_plan, user_request, target_os, system_context
        )

    def _sync_generate_correction_command(self, step_description: str, failed_history: List[Dict]) -> str:
        """Internal sync method to generate a correction command."""
        prompt = self._create_correction_prompt(step_description, failed_history)

        # Define the configuration for this specific call
        correction_config = {
            "temperature": 0.4,
            "stop_sequences": ["\n"]
        }

        try:
            command = self._execute_gemini_call(prompt, correction_config)

            # Clean up the response
            command = command.strip()
            if "```" in command:
                command = re.sub(r"```(bash|sh)?\s*([\s\S]*?)\s*```", r"\2", command).strip()

            return command if command else "NO_ALTERNATIVE"
        except Exception as e:
            print(f"Error calling Gemini API for correction: {e}")
            return "NO_ALTERNATIVE"

    async def generate_correction_command(self, step_description: str, failed_history: List[Dict]) -> str:
        """Asynchronously generates a correction command."""
        return await asyncio.to_thread(
            self._sync_generate_correction_command, step_description, failed_history
        )
    
    def _create_correction_prompt(self, step_description: str, failed_history: List[Dict]) -> str:
        # This function is correct and remains unchanged
        history_str = ""
        for i, attempt in enumerate(failed_history):
            history_str += f"\n--- ATTEMPT {i+1} ---\n"
            history_str += f"COMMAND: `{attempt['command']}`\n"
            history_str += f"STDOUT:\n{attempt['stdout']}\n"
            history_str += f"STDERR:\n{attempt['stderr']}\n"
            history_str += "---------------------\n"

        return f"""You are an expert Linux system administrator acting as a troubleshooter. A command failed during a task. Your goal is to provide a single, alternative command to fix the issue.

TASK STEP DESCRIPTION: {step_description}

The following command(s) have already failed:
{history_str}

Based on the error, provide a single, corrected command that is most likely to succeed.

RULES:
- Return ONLY the raw command string.
- DO NOT provide any explanation, comments, or JSON formatting.
- DO NOT use markdown like ```bash.
- If you cannot find a correction, return the text "NO_ALTERNATIVE".

Example Response:
apt-get install -y nginx

Your turn:
"""

    def _create_task_prompt(self, user_request: str, target_os: str, system_context: str) -> str:
        # This prompt is correct and remains unchanged
        return f"""You are an expert server system administrator. Generate a concise task breakdown for the following request.

REQUEST: {user_request}
TARGET OS: {target_os}

First, decide if you can fulfill the request.

If you CAN fulfill the request, generate a JSON response with this EXACT structure:
{{
  "task_name": "Brief descriptive name",
  "description": "Detailed explanation of what this will accomplishes",
  "estimated_time": "Estimated completion time (e.g., '5-10 minutes')",
  "steps": [
    {{
      "description": "Short & Clear description of what this step does",
      "command": "Actual command to run (be specific with paths and options, only for terminal commands)",
      "rollback_command": "command to revert changes made by the ran command || no rollback available",
      "danger_level": "safe/caution/dangerous",
      "interactive": false
    }}
  ],
  "conclusion": "Detailed conclusion, guidance & what's next"
}}

If the request IS NOT about ADMINISTRATING A VPS (e.g., it is unclear, nonsensical, dangerous, or outside your scope), you MUST generate a JSON response with this EXACT structure instead:
{{
  "message": "I don't understand the request. Please rephrase."
}}

REQUIREMENTS FOR A SUCCESSFUL TASK:
- Provide the minimum steps to accomplish the task.
- For Simple Tasks, provide 1 step (e.g., 'list packages', 'delete directory').
- If the task is a general message or unclear, Write an Error Message.
- Use proper commands for the target OS ({target_os}).
- Mark dangerous operations clearly ("dangerous").
- Be specific with file paths and command options based on the provided context.
- Consider security best practices.
- Include only important steps with commands needed to fulfill the task.
- Use non-interactive commands; if an interactive command is needed, set "interactive" to true (avoid if possible).
- For commands needing user data, use placeholders like @@PLACEHOLDER_NAME@@.

Return ONLY the JSON object, with no other text, comments, or explanations.
"""

    def _validate_and_parse_response(self, json_response: str) -> Optional[Dict]:
        # This validation logic is correct and remains unchanged
        try:
            match = re.search(r"```json\s*([\s\S]*?)\s*```", json_response)
            if match:
                json_response = match.group(1)
            json_response = json_response.strip()

            data = json.loads(json_response)
        except json.JSONDecodeError:
            print(f"✗ Failed to parse JSON from AI response.\nRaw Response:\n{json_response}")
            return {"is_error": True, "data": {"message": "The AI returned an invalid response. Please try again."}}

        try:
            validate(instance=data, schema=TASK_SCHEMA)
            return {"is_error": False, "data": data}
        except ValidationError:
            try:
                validate(instance=data, schema=ERROR_SCHEMA)
                return {"is_error": True, "data": data}
            except ValidationError as final_e:
                print(f"✗ AI response failed validation for both task and error formats.")
                print(f"  Validation Error: {final_e.message}")
                print(f"  Raw Response:\n{json_response}")
                return {"is_error": True, "data": {"message": "The AI's response was not in a recognizable format."}}

    async def get_cache_stats(self) -> Dict:
        """Get cache statistics (simplified version for compatibility)."""
        return {
            "cache_size": 0,
            "max_cache_size": 0,
            "ttl_seconds": 0,
            "note": "Caching disabled in simplified AI client"
        }

# Global AI client instance (initialized when needed)
_ai_client: Optional[AIClient] = None

def get_ai_client() -> AIClient:
    """Get or create global AI client instance."""
    global _ai_client
    if _ai_client is None:
        from config import settings
        _ai_client = AIClient(settings.gemini_api_key)
    return _ai_client
